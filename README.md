# Yishi SRS Manager

一个基于.NET 9的程序，用于定时获取萤石云的Access Token并通过SRS的ingest功能拉取萤石云视频流，实现token过期自动更新和2分钟无拉流后自动断开连接的功能。

## 功能特性

- **自动Token管理**: 定时获取和刷新萤石云Access Token
- **萤石云流拉取**: 使用SRS ingest功能自动拉取萤石云RTMP流
- **Token自动更新**: 当token过期时自动更新SRS中对应流的地址
- **空闲流管理**: 配置idle_streams和max_idle实现2分钟无拉流后自动断开
- **多流支持**: 支持同时管理多个萤石云设备的视频流
- **日志记录**: 详细的操作日志记录
- **配置化**: 支持通过配置文件自定义各种参数

## 系统要求

- .NET 9.0 或更高版本
- SRS服务器（支持HTTP API）
- 萤石云开放平台账号（AppKey和Secret）

## 配置说明

在运行程序前，请修改 `appsettings.json` 文件中的配置：

```json
{
  "YishiCloud": {
    "AppKey": "your_app_key_here",        // 萤石云AppKey
    "Secret": "your_secret_here",         // 萤石云Secret
    "BaseUrl": "https://open.ys7.com",    // 萤石云API基础URL
    "TokenRefreshIntervalMinutes": 110    // Token刷新间隔（分钟）
  },
  "SrsServer": {
    "BaseUrl": "http://localhost:1985",   // SRS服务器HTTP API地址
    "StreamTimeoutMinutes": 2,            // 无拉流超时时间（分钟）
    "MonitorIntervalSeconds": 30          // 监控检查间隔（秒）
  }
}
```

### 配置参数说明

#### YishiCloud 配置
- `AppKey`: 萤石云开放平台的AppKey
- `Secret`: 萤石云开放平台的Secret
- `BaseUrl`: 萤石云API的基础URL，通常为 `https://open.ys7.com`
- `TokenRefreshIntervalMinutes`: Token刷新间隔，建议设置为110分钟（Token有效期2小时，提前10分钟刷新）

#### SrsServer 配置
- `BaseUrl`: SRS服务器的HTTP API地址，默认为 `http://localhost:1985`
- `StreamTimeoutMinutes`: 无拉流超时时间，超过此时间没有播放客户端将断开发布者
- `MonitorIntervalSeconds`: 监控检查间隔，建议30秒

## 运行程序

1. **安装依赖**
   ```bash
   dotnet restore
   ```

2. **编译程序**
   ```bash
   dotnet build
   ```

3. **运行程序**
   ```bash
   dotnet run
   ```

## 工作原理

1. **Token管理**: 程序启动时获取萤石云Access Token，并根据配置的间隔自动刷新
2. **流监控**: 定期查询SRS服务器的流和客户端信息
3. **状态跟踪**: 跟踪每个流的播放客户端数量和最后活跃时间
4. **自动断开**: 当检测到流超过指定时间（默认2分钟）没有播放客户端时，自动断开该流的发布者

## 日志说明

程序会输出详细的日志信息，包括：
- Token刷新状态
- 流监控信息
- 客户端连接/断开事件
- 错误和异常信息

## SRS配置要求

确保SRS服务器启用了HTTP API功能，在SRS配置文件中添加：

```nginx
http_api {
    enabled on;
    listen 1985;
    crossdomain on;
}
```

## 故障排除

1. **Token获取失败**: 检查AppKey和Secret是否正确，网络连接是否正常
2. **SRS连接失败**: 检查SRS服务器地址和HTTP API是否启用
3. **流监控异常**: 检查SRS服务器状态和API响应

## 开发说明

项目结构：
- `Program.cs`: 程序入口点
- `Services/`: 服务层，包含Token服务、SRS管理服务和监控服务
- `Models/`: 数据模型
- `Configuration/`: 配置模型

## 许可证

本项目采用MIT许可证。
