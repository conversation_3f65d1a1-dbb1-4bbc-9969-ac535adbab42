using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using YishiSrsManager.Configuration;
using YishiSrsManager.Services;

namespace YishiSrsManager;

/// <summary>
/// 测试程序，用于验证各个服务的功能
/// </summary>
public class TestProgram
{
    public static async Task RunTests()
    {
        // 配置服务
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json")
            .Build();

        services.Configure<YishiCloudSettings>(configuration.GetSection("YishiCloud"));
        services.Configure<SrsServerSettings>(configuration.GetSection("SrsServer"));

        services.AddHttpClient<IYishiTokenService, YishiTokenService>();
        services.AddHttpClient<ISrsManagementService, SrsManagementService>();
        services.AddSingleton<IYishiTokenService, YishiTokenService>();
        services.AddSingleton<ISrsManagementService, SrsManagementService>();

        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<TestProgram>>();

        logger.LogInformation("开始测试程序...");

        // 测试萤石云Token服务
        await TestYishiTokenService(serviceProvider, logger);

        // 测试SRS管理服务
        await TestSrsManagementService(serviceProvider, logger);

        logger.LogInformation("测试程序完成");
    }

    private static async Task TestYishiTokenService(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("=== 测试萤石云Token服务 ===");

        try
        {
            var tokenService = serviceProvider.GetRequiredService<IYishiTokenService>();

            // 测试获取Token
            logger.LogInformation("正在获取Access Token...");
            var token = await tokenService.GetValidTokenAsync();

            if (!string.IsNullOrEmpty(token))
            {
                logger.LogInformation("✅ Token获取成功: {Token}", token[..Math.Min(20, token.Length)] + "...");
            }
            else
            {
                logger.LogError("❌ Token获取失败");
            }

            // 测试刷新Token
            logger.LogInformation("正在测试Token刷新...");
            var refreshResult = await tokenService.RefreshTokenAsync();
            logger.LogInformation(refreshResult ? "✅ Token刷新成功" : "❌ Token刷新失败");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ 萤石云Token服务测试失败");
        }
    }

    private static async Task TestSrsManagementService(IServiceProvider serviceProvider, ILogger logger)
    {
        logger.LogInformation("=== 测试SRS管理服务 ===");

        try
        {
            var srsService = serviceProvider.GetRequiredService<ISrsManagementService>();

            // 测试获取客户端列表
            logger.LogInformation("正在获取SRS客户端列表...");
            var clients = await srsService.GetClientsAsync();
            logger.LogInformation("✅ 获取到 {Count} 个客户端", clients.Count);

            foreach (var client in clients.Take(5)) // 只显示前5个
            {
                logger.LogInformation("  客户端: {Id}, 流: {Stream}, 类型: {Type}, 发布: {Publish}",
                    client.Id, client.Stream, client.Type, client.Publish);
            }

            // 测试获取流列表
            logger.LogInformation("正在获取SRS流列表...");
            var streams = await srsService.GetStreamsAsync();
            logger.LogInformation("✅ 获取到 {Count} 个流", streams.Count);

            foreach (var stream in streams.Take(5)) // 只显示前5个
            {
                logger.LogInformation("  流: {Name}, 客户端数: {Clients}, 存活时间: {LiveMs}ms",
                    stream.Name, stream.Clients, stream.LiveMs);
            }

            // 测试获取流监控信息
            logger.LogInformation("正在获取流监控信息...");
            var monitorInfo = await srsService.GetStreamMonitorInfoAsync();
            logger.LogInformation("✅ 获取到 {Count} 个流的监控信息", monitorInfo.Count);

            foreach (var info in monitorInfo.Take(5)) // 只显示前5个
            {
                logger.LogInformation("  流: {Name}, 播放客户端: {Clients}, 有发布者: {HasPublisher}",
                    info.StreamName, info.ClientCount, info.HasPublisher);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ SRS管理服务测试失败");
        }
    }
}

// 可以通过以下方式运行测试：
// await TestProgram.RunTests();
