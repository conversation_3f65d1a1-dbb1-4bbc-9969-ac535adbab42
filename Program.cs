using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using YishiSrsManager.Configuration;
using YishiSrsManager.Services;

namespace YishiSrsManager;

class Program
{
    static async Task Main(string[] args)
    {
        var host = CreateHostBuilder(args).Build();

        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("Yishi SRS Manager starting...");

        try
        {
            await host.RunAsync();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Application terminated unexpectedly");
        }
        finally
        {
            logger.LogInformation("Yishi SRS Manager stopped");
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置选项
                services.Configure<YishiCloudSettings>(
                    context.Configuration.GetSection("YishiCloud"));
                services.Configure<SrsServerSettings>(
                    context.Configuration.GetSection("SrsServer"));

                // 添加IConfiguration服务
                services.AddSingleton<IConfiguration>(context.Configuration);

                // HTTP客户端
                services.AddHttpClient<IYishiTokenService, YishiTokenService>();
                services.AddHttpClient<ISrsManagementService, SrsManagementService>();

                // 服务注册
                services.AddSingleton<IYishiTokenService, YishiTokenService>();
                services.AddSingleton<ISrsManagementService, SrsManagementService>();

                // 后台服务
                services.AddHostedService<StreamMonitorService>();

                // 日志配置
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            })
            .UseConsoleLifetime();
}
