version: '3.8'

services:
  yishi-srs-manager:
    build: .
    container_name: yishi-srs-manager
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./appsettings.json:/app/appsettings.json:ro
      - ./logs:/app/logs
    networks:
      - srs-network
    depends_on:
      - srs
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # SRS服务器示例配置（可选）
  srs:
    image: ossrs/srs:5
    container_name: srs-server
    restart: unless-stopped
    ports:
      - "1935:1935"   # RTMP端口
      - "1985:1985"   # HTTP API端口
      - "8080:8080"   # HTTP服务端口
    volumes:
      - ./srs.conf:/usr/local/srs/conf/srs.conf:ro
    networks:
      - srs-network
    command: ["./objs/srs", "-c", "conf/srs.conf"]

networks:
  srs-network:
    driver: bridge
