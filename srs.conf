# SRS配置文件示例
listen              1935;
max_connections     1000;
srs_log_tank        file;
srs_log_file        ./objs/srs.log;

# HTTP API配置
http_api {
    enabled         on;
    listen          1985;
    crossdomain     on;
}

# HTTP服务器配置
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

# 统计配置
stats {
    network         0;
    disk            sda sdb xvda xvdb;
}

# 虚拟主机配置
vhost __defaultVhost__ {
    # HLS配置
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
    
    # HTTP回调配置（可选）
    http_hooks {
        enabled         on;
        on_connect      http://yishi-srs-manager:8080/api/v1/clients;
        on_close        http://yishi-srs-manager:8080/api/v1/clients;
        on_publish      http://yishi-srs-manager:8080/api/v1/streams;
        on_unpublish    http://yishi-srs-manager:8080/api/v1/streams;
        on_play         http://yishi-srs-manager:8080/api/v1/sessions;
        on_stop         http://yishi-srs-manager:8080/api/v1/sessions;
    }
    
    # 安全配置
    security {
        enabled         off;
        seo {
            enabled     on;
            summary     "SRS streaming server";
        }
    }
}
