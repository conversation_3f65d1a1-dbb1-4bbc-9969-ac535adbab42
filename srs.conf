# SRS配置文件 - 支持萤石云流管理
listen              1935;
max_connections     1000;
srs_log_tank        file;
srs_log_file        ./objs/srs.log;

# HTTP API配置
http_api {
    enabled         on;
    listen          1985;
    crossdomain     on;
}

# HTTP服务器配置
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

# 统计配置
stats {
    network         0;
    disk            sda sdb xvda xvdb;
}

# 虚拟主机配置
vhost __defaultVhost__ {
    # 启用ingest功能
    ingest {
        enabled         on;
    }

    # 流空闲管理配置
    play {
        # 启用空闲流管理
        idle_streams    on;
        # 空闲超时时间（秒），2分钟 = 120秒
        max_idle        120;
    }

    # HLS配置
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }

    # HTTP-FLV配置
    http_remux {
        enabled         on;
        mount           [vhost]/[app]/[stream].flv;
    }

    # DVR配置（可选）
    dvr {
        enabled         off;
        dvr_path        ./objs/nginx/html/[app]/[stream]/[2006]/[01]/[02]/[15].[04].[05].[999].flv;
        dvr_plan        session;
        dvr_duration    30;
        dvr_wait_keyframe on;
    }

    # 安全配置
    security {
        enabled         off;
        seo {
            enabled     on;
            summary     "SRS streaming server with Yishi Cloud integration";
        }
    }
}
