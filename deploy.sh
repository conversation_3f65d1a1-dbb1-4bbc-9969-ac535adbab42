#!/bin/bash

# Yishi SRS Manager 部署脚本

set -e

echo "🚀 开始部署 Yishi SRS Manager..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查配置文件
if [ ! -f "appsettings.json" ]; then
    echo "❌ appsettings.json 配置文件不存在"
    echo "请复制 appsettings.json 并配置正确的 AppKey 和 Secret"
    exit 1
fi

# 检查萤石云配置
APPKEY=$(grep -o '"AppKey": "[^"]*"' appsettings.json | cut -d'"' -f4)
SECRET=$(grep -o '"Secret": "[^"]*"' appsettings.json | cut -d'"' -f4)

if [ "$APPKEY" = "your_app_key_here" ] || [ "$SECRET" = "your_secret_here" ]; then
    echo "❌ 请在 appsettings.json 中配置正确的萤石云 AppKey 和 Secret"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 创建日志目录
mkdir -p logs

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示日志
echo "📋 显示最近的日志..."
docker-compose logs --tail=20 yishi-srs-manager

echo ""
echo "🎉 部署完成！"
echo ""
echo "📝 常用命令："
echo "  查看日志: docker-compose logs -f yishi-srs-manager"
echo "  重启服务: docker-compose restart yishi-srs-manager"
echo "  停止服务: docker-compose down"
echo "  查看状态: docker-compose ps"
echo ""
echo "🌐 SRS服务地址："
echo "  RTMP推流: rtmp://localhost:1935/live/stream_name"
echo "  HTTP API: http://localhost:1985/api/v1/"
echo "  Web播放: http://localhost:8080/"
