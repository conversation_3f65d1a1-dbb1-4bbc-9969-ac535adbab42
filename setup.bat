@echo off
chcp 65001 >nul
echo 🚀 Yishi SRS Manager 快速设置
echo ================================

REM 检查是否存在配置文件
if not exist "appsettings.json" (
    echo 📝 创建配置文件...
    copy appsettings.example.json appsettings.json >nul
    echo ✅ 已创建 appsettings.json，请编辑此文件配置您的萤石云信息
    echo.
    echo ⚠️  请修改以下配置项：
    echo    - YishiCloud.AppKey: 您的萤石云AppKey
    echo    - YishiCloud.Secret: 您的萤石云Secret
    echo    - YishiStreams: 您的设备配置
    echo.
    pause
)

REM 检查.NET 9
echo 🔍 检查.NET 9...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 .NET SDK
    echo 请从以下地址下载并安装 .NET 9 SDK:
    echo https://dotnet.microsoft.com/download/dotnet/9.0
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ 找到 .NET SDK: %DOTNET_VERSION%

REM 还原依赖
echo 📦 还原 NuGet 包...
dotnet restore

REM 构建项目
echo 🔨 构建项目...
dotnet build -c Release

echo.
echo 🎉 设置完成！
echo.
echo 📋 下一步：
echo 1. 编辑 appsettings.json 配置您的萤石云信息
echo 2. 确保 SRS 服务器正在运行并启用了 HTTP API
echo 3. 运行程序: dotnet run
echo.
echo 📖 更多信息请查看 README.md

pause
