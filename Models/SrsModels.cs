using System.Text.Json.Serialization;

namespace YishiSrsManager.Models;

public class SrsClientsResponse
{
    [JsonPropertyName("code")]
    public int Code { get; set; }

    [JsonPropertyName("clients")]
    public List<SrsClient> Clients { get; set; } = new();
}

public class SrsClient
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("vhost")]
    public string VHost { get; set; } = string.Empty;

    [JsonPropertyName("stream")]
    public string Stream { get; set; } = string.Empty;

    [JsonPropertyName("ip")]
    public string Ip { get; set; } = string.Empty;

    [JsonPropertyName("pageUrl")]
    public string PageUrl { get; set; } = string.Empty;

    [JsonPropertyName("swfUrl")]
    public string SwfUrl { get; set; } = string.Empty;

    [JsonPropertyName("tcUrl")]
    public string TcUrl { get; set; } = string.Empty;

    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("publish")]
    public bool Publish { get; set; }

    [JsonPropertyName("alive")]
    public long Alive { get; set; }
}

public class SrsStreamsResponse
{
    [JsonPropertyName("code")]
    public int Code { get; set; }

    [JsonPropertyName("streams")]
    public List<SrsStream> Streams { get; set; } = new();
}

public class SrsStream
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("vhost")]
    public string VHost { get; set; } = string.Empty;

    [JsonPropertyName("app")]
    public string App { get; set; } = string.Empty;

    [JsonPropertyName("live_ms")]
    public long LiveMs { get; set; }

    [JsonPropertyName("clients")]
    public int Clients { get; set; }

    [JsonPropertyName("frames")]
    public long Frames { get; set; }

    [JsonPropertyName("send_bytes")]
    public long SendBytes { get; set; }

    [JsonPropertyName("recv_bytes")]
    public long RecvBytes { get; set; }

    [JsonPropertyName("kbps")]
    public SrsKbps? Kbps { get; set; }
}

public class SrsKbps
{
    [JsonPropertyName("recv_30s")]
    public int Recv30s { get; set; }

    [JsonPropertyName("send_30s")]
    public int Send30s { get; set; }
}

public class StreamMonitorInfo
{
    public string StreamId { get; set; } = string.Empty;
    public string StreamName { get; set; } = string.Empty;
    public DateTime LastClientTime { get; set; }
    public int ClientCount { get; set; }
    public bool HasPublisher { get; set; }
}
