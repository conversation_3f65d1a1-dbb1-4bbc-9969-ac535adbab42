using System.Text.Json.Serialization;

namespace YishiSrsManager.Models;

public class YishiTokenRequest
{
    [JsonPropertyName("appKey")]
    public string AppKey { get; set; } = string.Empty;

    [JsonPropertyName("secret")]
    public string Secret { get; set; } = string.Empty;
}

public class YishiTokenResponse
{
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;

    [JsonPropertyName("msg")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("data")]
    public YishiTokenData? Data { get; set; }
}

public class YishiTokenData
{
    [JsonPropertyName("accessToken")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonPropertyName("expireTime")]
    public long ExpireTime { get; set; }
}

public class TokenInfo
{
    public string AccessToken { get; set; } = string.Empty;
    public DateTime ExpireTime { get; set; }
    public bool IsExpired => DateTime.Now >= ExpireTime.AddMinutes(-10); // 提前10分钟判断过期
}
