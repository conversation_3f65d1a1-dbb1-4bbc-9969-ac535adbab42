@echo off
chcp 65001 >nul
echo 🚀 启动 Yishi SRS Manager...

REM 检查.NET 9是否安装
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 9 未安装，请先安装 .NET 9 SDK
    echo 下载地址: https://dotnet.microsoft.com/download/dotnet/9.0
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "appsettings.json" (
    echo ❌ appsettings.json 配置文件不存在
    echo 请确保配置文件存在并配置正确的 AppKey 和 Secret
    pause
    exit /b 1
)

REM 还原依赖
echo 📦 还原依赖包...
dotnet restore

REM 构建项目
echo 🔨 构建项目...
dotnet build -c Release

REM 运行程序
echo ▶️ 启动程序...
echo.
dotnet run -c Release

pause
