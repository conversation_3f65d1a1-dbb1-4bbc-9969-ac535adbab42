namespace YishiSrsManager.Configuration;

public class YishiCloudSettings
{
    public string AppKey { get; set; } = string.Empty;
    public string Secret { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = "https://open.ys7.com";
    public int TokenRefreshIntervalMinutes { get; set; } = 110; // Token有效期通常是2小时，提前10分钟刷新
}

public class SrsServerSettings
{
    public string BaseUrl { get; set; } = "http://localhost:1985";
    public int StreamTimeoutMinutes { get; set; } = 2;
    public int MonitorIntervalSeconds { get; set; } = 30;
}
