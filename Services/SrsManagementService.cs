using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using YishiSrsManager.Configuration;
using YishiSrsManager.Models;

namespace YishiSrsManager.Services;

public interface ISrsManagementService
{
    Task<List<SrsClient>> GetClientsAsync();
    Task<List<SrsStream>> GetStreamsAsync();
    Task<bool> KickClientAsync(string clientId);
    Task<List<StreamMonitorInfo>> GetStreamMonitorInfoAsync();
}

public class SrsManagementService : ISrsManagementService
{
    private readonly HttpClient _httpClient;
    private readonly SrsServerSettings _settings;
    private readonly ILogger<SrsManagementService> _logger;

    public SrsManagementService(
        HttpClient httpClient,
        IOptions<SrsServerSettings> settings,
        ILogger<SrsManagementService> logger)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<List<SrsClient>> GetClientsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_settings.BaseUrl}/api/v1/clients/");
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to get clients. Status: {StatusCode}", response.StatusCode);
                return new List<SrsClient>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var clientsResponse = JsonSerializer.Deserialize<SrsClientsResponse>(content);

            return clientsResponse?.Clients ?? new List<SrsClient>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SRS clients");
            return new List<SrsClient>();
        }
    }

    public async Task<List<SrsStream>> GetStreamsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_settings.BaseUrl}/api/v1/streams/");
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to get streams. Status: {StatusCode}", response.StatusCode);
                return new List<SrsStream>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var streamsResponse = JsonSerializer.Deserialize<SrsStreamsResponse>(content);

            return streamsResponse?.Streams ?? new List<SrsStream>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SRS streams");
            return new List<SrsStream>();
        }
    }

    public async Task<bool> KickClientAsync(string clientId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_settings.BaseUrl}/api/v1/clients/{clientId}");
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully kicked client {ClientId}", clientId);
                return true;
            }
            else
            {
                _logger.LogError("Failed to kick client {ClientId}. Status: {StatusCode}", 
                    clientId, response.StatusCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error kicking client {ClientId}", clientId);
            return false;
        }
    }

    public async Task<List<StreamMonitorInfo>> GetStreamMonitorInfoAsync()
    {
        var streams = await GetStreamsAsync();
        var clients = await GetClientsAsync();
        var result = new List<StreamMonitorInfo>();

        foreach (var stream in streams)
        {
            var streamClients = clients.Where(c => c.Stream == stream.Name).ToList();
            var playClients = streamClients.Where(c => !c.Publish).ToList();
            var hasPublisher = streamClients.Any(c => c.Publish);

            var monitorInfo = new StreamMonitorInfo
            {
                StreamId = stream.Id,
                StreamName = stream.Name,
                ClientCount = playClients.Count,
                HasPublisher = hasPublisher,
                LastClientTime = playClients.Any() ? 
                    DateTime.Now : // 如果有客户端，使用当前时间
                    DateTime.Now.AddMinutes(-_settings.StreamTimeoutMinutes - 1) // 如果没有客户端，设置为超时时间
            };

            result.Add(monitorInfo);
        }

        return result;
    }
}
