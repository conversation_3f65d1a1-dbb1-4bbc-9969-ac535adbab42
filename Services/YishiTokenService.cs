using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using YishiSrsManager.Configuration;
using YishiSrsManager.Models;

namespace YishiSrsManager.Services;

public interface IYishiTokenService
{
    Task<string?> GetValidTokenAsync();
    Task<bool> RefreshTokenAsync();
}

public class YishiTokenService : IYishiTokenService
{
    private readonly HttpClient _httpClient;
    private readonly YishiCloudSettings _settings;
    private readonly ILogger<YishiTokenService> _logger;
    private TokenInfo? _currentToken;
    private readonly SemaphoreSlim _tokenSemaphore = new(1, 1);

    public YishiTokenService(
        HttpClient httpClient,
        IOptions<YishiCloudSettings> settings,
        ILogger<YishiTokenService> logger)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<string?> GetValidTokenAsync()
    {
        await _tokenSemaphore.WaitAsync();
        try
        {
            if (_currentToken == null || _currentToken.IsExpired)
            {
                _logger.LogInformation("Token is null or expired, refreshing...");
                await RefreshTokenInternalAsync();
            }

            return _currentToken?.AccessToken;
        }
        finally
        {
            _tokenSemaphore.Release();
        }
    }

    public async Task<bool> RefreshTokenAsync()
    {
        await _tokenSemaphore.WaitAsync();
        try
        {
            return await RefreshTokenInternalAsync();
        }
        finally
        {
            _tokenSemaphore.Release();
        }
    }

    private async Task<bool> RefreshTokenInternalAsync()
    {
        try
        {
            var request = new YishiTokenRequest
            {
                AppKey = _settings.AppKey,
                Secret = _settings.Secret
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogInformation("Requesting new token from Yishi Cloud...");
            var response = await _httpClient.PostAsync($"{_settings.BaseUrl}/api/lapp/token/get", content);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to get token. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, await response.Content.ReadAsStringAsync());
                return false;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<YishiTokenResponse>(responseContent);

            if (tokenResponse?.Code != "200" || tokenResponse.Data == null)
            {
                _logger.LogError("Token request failed. Code: {Code}, Message: {Message}",
                    tokenResponse?.Code, tokenResponse?.Message);
                return false;
            }

            _currentToken = new TokenInfo
            {
                AccessToken = tokenResponse.Data.AccessToken,
                ExpireTime = DateTimeOffset.FromUnixTimeMilliseconds(tokenResponse.Data.ExpireTime).DateTime
            };

            _logger.LogInformation("Token refreshed successfully. Expires at: {ExpireTime}", _currentToken.ExpireTime);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing token");
            return false;
        }
    }
}
