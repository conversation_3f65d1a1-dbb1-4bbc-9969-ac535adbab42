using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using YishiSrsManager.Configuration;
using YishiSrsManager.Models;

namespace YishiSrsManager.Services;

public class StreamMonitorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly SrsServerSettings _srsSettings;
    private readonly YishiCloudSettings _yishiSettings;
    private readonly ILogger<StreamMonitorService> _logger;
    private readonly Dictionary<string, StreamMonitorInfo> _streamStates = new();

    public StreamMonitorService(
        IServiceProvider serviceProvider,
        IOptions<SrsServerSettings> srsSettings,
        IOptions<YishiCloudSettings> yishiSettings,
        ILogger<StreamMonitorService> logger)
    {
        _serviceProvider = serviceProvider;
        _srsSettings = srsSettings.Value;
        _yishiSettings = yishiSettings.Value;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Stream Monitor Service started");

        // 启动时先刷新一次Token
        using (var scope = _serviceProvider.CreateScope())
        {
            var tokenService = scope.ServiceProvider.GetRequiredService<IYishiTokenService>();
            await tokenService.RefreshTokenAsync();
        }

        var tokenRefreshTimer = TimeSpan.FromMinutes(_yishiSettings.TokenRefreshIntervalMinutes);
        var monitorTimer = TimeSpan.FromSeconds(_srsSettings.MonitorIntervalSeconds);
        var lastTokenRefresh = DateTime.Now;

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var tokenService = scope.ServiceProvider.GetRequiredService<IYishiTokenService>();
                var srsService = scope.ServiceProvider.GetRequiredService<ISrsManagementService>();

                // 检查是否需要刷新Token
                if (DateTime.Now - lastTokenRefresh >= tokenRefreshTimer)
                {
                    _logger.LogInformation("Refreshing Yishi Cloud token...");
                    await tokenService.RefreshTokenAsync();
                    lastTokenRefresh = DateTime.Now;
                }

                // 监控流状态
                await MonitorStreamsAsync(srsService);

                await Task.Delay(monitorTimer, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in stream monitor service");
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }

        _logger.LogInformation("Stream Monitor Service stopped");
    }

    private async Task MonitorStreamsAsync(ISrsManagementService srsService)
    {
        try
        {
            var currentStreams = await srsService.GetStreamMonitorInfoAsync();
            var timeoutThreshold = TimeSpan.FromMinutes(_srsSettings.StreamTimeoutMinutes);

            foreach (var stream in currentStreams)
            {
                var streamKey = $"{stream.StreamName}";

                // 更新流状态
                if (_streamStates.ContainsKey(streamKey))
                {
                    var existingState = _streamStates[streamKey];
                    
                    // 如果有客户端连接，更新最后客户端时间
                    if (stream.ClientCount > 0)
                    {
                        existingState.LastClientTime = DateTime.Now;
                        existingState.ClientCount = stream.ClientCount;
                    }
                    else
                    {
                        existingState.ClientCount = 0;
                    }

                    existingState.HasPublisher = stream.HasPublisher;
                }
                else
                {
                    // 新流
                    _streamStates[streamKey] = new StreamMonitorInfo
                    {
                        StreamId = stream.StreamId,
                        StreamName = stream.StreamName,
                        LastClientTime = stream.ClientCount > 0 ? DateTime.Now : DateTime.Now.AddMinutes(-_srsSettings.StreamTimeoutMinutes - 1),
                        ClientCount = stream.ClientCount,
                        HasPublisher = stream.HasPublisher
                    };
                    
                    _logger.LogInformation("New stream detected: {StreamName}", stream.StreamName);
                }
            }

            // 检查需要断开的流
            var streamsToDisconnect = new List<string>();
            
            foreach (var kvp in _streamStates.ToList())
            {
                var streamState = kvp.Value;
                var timeSinceLastClient = DateTime.Now - streamState.LastClientTime;

                // 如果流有发布者但超过指定时间没有播放客户端，则断开发布者
                if (streamState.HasPublisher && streamState.ClientCount == 0 && timeSinceLastClient >= timeoutThreshold)
                {
                    _logger.LogInformation("Stream {StreamName} has no play clients for {Minutes} minutes, disconnecting publisher...", 
                        streamState.StreamName, _srsSettings.StreamTimeoutMinutes);
                    
                    await DisconnectStreamPublisherAsync(srsService, streamState.StreamName);
                    streamsToDisconnect.Add(kvp.Key);
                }
                // 如果流没有发布者，从监控列表中移除
                else if (!streamState.HasPublisher)
                {
                    var currentStreamExists = currentStreams.Any(s => s.StreamName == streamState.StreamName);
                    if (!currentStreamExists)
                    {
                        streamsToDisconnect.Add(kvp.Key);
                        _logger.LogInformation("Stream {StreamName} no longer exists, removing from monitor", streamState.StreamName);
                    }
                }
            }

            // 移除已断开的流
            foreach (var streamKey in streamsToDisconnect)
            {
                _streamStates.Remove(streamKey);
            }

            // 记录当前状态
            if (_streamStates.Any())
            {
                _logger.LogDebug("Monitoring {Count} streams", _streamStates.Count);
                foreach (var stream in _streamStates.Values)
                {
                    var timeSinceLastClient = DateTime.Now - stream.LastClientTime;
                    _logger.LogDebug("Stream: {StreamName}, Clients: {ClientCount}, Publisher: {HasPublisher}, Last Client: {Minutes}m ago",
                        stream.StreamName, stream.ClientCount, stream.HasPublisher, (int)timeSinceLastClient.TotalMinutes);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring streams");
        }
    }

    private async Task DisconnectStreamPublisherAsync(ISrsManagementService srsService, string streamName)
    {
        try
        {
            var clients = await srsService.GetClientsAsync();
            var publishers = clients.Where(c => c.Stream == streamName && c.Publish).ToList();

            foreach (var publisher in publishers)
            {
                _logger.LogInformation("Kicking publisher client {ClientId} for stream {StreamName}", 
                    publisher.Id, streamName);
                await srsService.KickClientAsync(publisher.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting publisher for stream {StreamName}", streamName);
        }
    }
}
