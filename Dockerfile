# 使用官方的 .NET 9 运行时镜像作为基础镜像
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

# 使用官方的 .NET 9 SDK 镜像进行构建
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件并还原依赖
COPY ["YishiSrsManager.csproj", "."]
RUN dotnet restore "YishiSrsManager.csproj"

# 复制所有源代码
COPY . .

# 构建应用程序
RUN dotnet build "YishiSrsManager.csproj" -c Release -o /app/build

# 发布应用程序
FROM build AS publish
RUN dotnet publish "YishiSrsManager.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 最终镜像
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 设置时区为中国标准时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

ENTRYPOINT ["dotnet", "YishiSrsManager.dll"]
